{"mappings": ";AAGA,gCAAgC,CAAC,IAAI;IACnC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;IACrB,WAAW,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;IAC5B,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;CAC/B,CAAC;AAIF,qCAA8B,CAAC,EAAE,EAC/B,IAAI,EACJ,WAAW,EACX,QAAmB,GACpB,EAAE,2BAA2B,CAAC,CAAC,iFAoB/B", "sources": ["packages/react/use-controllable-state/src/packages/react/use-controllable-state/src/useControllableState.tsx", "packages/react/use-controllable-state/src/packages/react/use-controllable-state/src/index.ts", "packages/react/use-controllable-state/src/index.ts"], "sourcesContent": [null, null, "export { useControllableState } from './useControllableState';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}