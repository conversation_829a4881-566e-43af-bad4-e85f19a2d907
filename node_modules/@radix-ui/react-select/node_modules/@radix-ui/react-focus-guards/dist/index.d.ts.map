{"mappings": "AAKA,4BAAqB,KAAK,EAAE,GAAG,OAG9B;AAED;;;GAGG;AACH,uCAcC;AAUD,OAAA,MAAM,wBAAkB,CAAC", "sources": ["packages/react/focus-guards/src/packages/react/focus-guards/src/FocusGuards.tsx", "packages/react/focus-guards/src/packages/react/focus-guards/src/index.ts", "packages/react/focus-guards/src/index.ts"], "sourcesContent": [null, null, "export {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n} from './FocusGuards';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}