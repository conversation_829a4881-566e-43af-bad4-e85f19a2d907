{"mappings": "ACAA,SAASA,yCAAT,CACEC,oBADF,EAEEC,eAFF,EAGE,4BAAEC,wBAAwB,GAAG,IAA3BA,GAAF,GAAsC,EAHxC,EAIE;IACA,OAAO,SAASC,WAAT,CAAqBC,KAArB,EAA+B;QACpCJ,oBAAoB,KAAA,IAApB,IAAAA,oBAAoB,KAAA,KAAA,CAApB,IAAAA,oBAAoB,CAAGI,KAAH,CAApB,CAAAJ;QAEA,IAAIE,wBAAwB,KAAK,KAA7B,IAAsC,CAAGE,KAAF,CAA8BC,gBAAzE,EACE,OAAOJ,eAAP,KAAA,IAAA,IAAOA,eAAP,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAOA,eAAe,CAAGG,KAAH,CAAtB,CAAA;KAJJ,CAMC;CACF;;ADZD", "sources": ["packages/core/primitive/src/index.ts", "packages/core/primitive/src/primitive.tsx"], "sourcesContent": ["export { composeEventHandlers } from './primitive';\n", "function composeEventHandlers<E>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !((event as unknown) as Event).defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n"], "names": ["composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "handleEvent", "event", "defaultPrevented"], "version": 3, "file": "index.mjs.map"}