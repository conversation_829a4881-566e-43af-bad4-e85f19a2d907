{"mappings": ";;;;;;;;;;;;;;A;;;ACGA;;oGAEA,CAMA,MAAMA,yCAAI,GAAA,aAAGG,CAAAA,uBAAA,CAAyC,CAACG,KAAD,EAAQC,YAAR,GAAyB;IAC7E,MAAM,E,UAAEC,QAAF,CAAA,EAAY,GAAGC,SAAH,EAAZ,GAA6BH,KAAnC,AAAM;IACN,MAAMI,aAAa,GAAGP,qBAAA,CAAeS,OAAf,CAAuBJ,QAAvB,CAAtB,AAAA;IACA,MAAMK,SAAS,GAAGH,aAAa,CAACI,IAAd,CAAmBC,iCAAnB,CAAlB,AAAA;IAEA,IAAIF,SAAJ,EAAe;QACb,wEAAA;QACA,MAAMG,UAAU,GAAGH,SAAS,CAACP,KAAV,CAAgBE,QAAnC,AAAA;QAEA,MAAMS,WAAW,GAAGP,aAAa,CAACQ,GAAd,CAAmBC,CAAAA,KAAD,GAAW;YAC/C,IAAIA,KAAK,KAAKN,SAAd,EAAyB;gBACvB,2EAAA;gBACA,yDAAA;gBACA,IAAIV,qBAAA,CAAeiB,KAAf,CAAqBJ,UAArB,CAAA,GAAmC,CAAvC,EAA0C,OAAOb,qBAAA,CAAekB,IAAf,CAAoB,IAApB,CAAP,CAA1C;gBACA,OAAO,aAAAlB,CAAAA,2BAAA,CAAqBa,UAArB,CAAA,GACFA,UAAU,CAACV,KAAX,CAAiBE,QADf,GAEH,IAFJ,CAAA;aAJF,MAQE,OAAOW,KAAP,CAAA;SATgB,CAApB,AAWC;QAED,OAAA,aACE,CAAA,0BAAA,CAAC,+BAAD,EAAA,2DAAA,CAAA,EAAA,EAAeV,SAAf,EADF;YAC4B,GAAG,EAAEF,YAAL;SAA1B,CAAA,EACG,aAAAJ,CAAAA,2BAAA,CAAqBa,UAArB,CAAA,GAAA,aACGb,CAAAA,yBAAA,CAAmBa,UAAnB,EAA+BQ,SAA/B,EAA0CP,WAA1C,CADH,GAEG,IAHN,CADF,CACE;KAMH;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,+BAAD,EAAA,2DAAA,CAAA,EAAA,EAAeR,SAAf,EADF;QAC4B,GAAG,EAAEF,YAAL;KAA1B,CAAA,EACGC,QADH,CADF,CACE;CAhCS,CAAb,AAoCC;AAEDR,yCAAI,CAACyB,WAAL,GAAmB,MAAnB,CAAAzB;AAEA;;oGAEA,CAMA,MAAM0B,+BAAS,GAAA,aAAGvB,CAAAA,uBAAA,CAAsC,CAACG,KAAD,EAAQC,YAAR,GAAyB;IAC/E,MAAM,E,UAAEC,QAAF,CAAA,EAAY,GAAGC,SAAH,EAAZ,GAA6BH,KAAnC,AAAM;IAEN,IAAA,aAAIH,CAAAA,2BAAA,CAAqBK,QAArB,CAAJ,EACE,OAAA,aAAOL,CAAAA,yBAAA,CAAmBK,QAAnB,EAA6B;QAClC,GAAGmB,gCAAU,CAAClB,SAAD,EAAYD,QAAQ,CAACF,KAArB,CADqB;QAElCsB,GAAG,EAAErB,YAAY,GAAGH,0CAAW,CAACG,YAAD,EAAgBC,QAAD,CAAkBoB,GAAjC,CAAd,GAAuDpB,QAAD,CAAkBoB,GAAzFA;KAFK,CAAP,CAAoC;IAMtC,OAAOzB,qBAAA,CAAeiB,KAAf,CAAqBZ,QAArB,CAAA,GAAiC,CAAjC,GAAqCL,qBAAA,CAAekB,IAAf,CAAoB,IAApB,CAArC,GAAiE,IAAxE,CAAA;CAVgB,CAAlB,AAWC;AAEDK,+BAAS,CAACD,WAAV,GAAwB,WAAxB,CAAAC;AAEA;;oGAEA,CAEA,MAAMzB,yCAAS,GAAG,CAAC,E,UAAEO,QAAAA,CAAAA,EAAH,GAAiD;IACjE,OAAA,aAAO,CAAA,0BAAA,CAAA,qBAAA,EAAA,IAAA,EAAGA,QAAH,CAAP,CAAA;CADF,AAEC;AAED,oGAAA,CAIA,SAASO,iCAAT,CAAqBI,KAArB,EAA0E;IACxE,OAAO,aAAAhB,CAAAA,2BAAA,CAAqBgB,KAArB,CAAA,IAA+BA,KAAK,CAACU,IAAN,KAAe5B,yCAArD,CAAA;CACD;AAED,SAAS0B,gCAAT,CAAoBlB,SAApB,EAAyCqB,UAAzC,EAA+D;IAC7D,kCAAA;IACA,MAAMC,aAAa,GAAG;QAAE,GAAGD,UAA3B;KAAA,AAAsB;IAEtB,IAAK,MAAME,QAAX,IAAuBF,UAAvB,CAAmC;QACjC,MAAMG,aAAa,GAAGxB,SAAS,CAACuB,QAAD,CAA/B,AAAA;QACA,MAAME,cAAc,GAAGJ,UAAU,CAACE,QAAD,CAAjC,AAAA;QAEA,MAAMG,SAAS,GAAG,WAAWC,IAAX,CAAgBJ,QAAhB,CAAlB,AAAA;QACA,IAAIG,SAAJ,EAAe;YACb,iDAAA;YACA,IAAIF,aAAa,IAAIC,cAArB,EACEH,aAAa,CAACC,QAAD,CAAb,GAA0B,CAAIK,GAAAA,IAAJ,GAAwB;gBAChDH,cAAc,IAAIG,IAAJ,CAAd,CAAAH;gBACAD,aAAa,IAAII,IAAJ,CAAb,CAAAJ;aAFF,CAGC;iBAGE,IAAIA,aAAJ,EACHF,aAAa,CAACC,QAAD,CAAb,GAA0BC,aAA1B,CAAAF;SAVJ,MAcK,IAAIC,QAAQ,KAAK,OAAjB,EACHD,aAAa,CAACC,QAAD,CAAb,GAA0B;YAAE,GAAGC,aAAL;YAAoB,GAAGC,cAAH;SAA9C,CAA0B;aACrB,IAAIF,QAAQ,KAAK,WAAjB,EACLD,aAAa,CAACC,QAAD,CAAb,GAA0B;YAACC,aAAD;YAAgBC,cAAhB;SAAA,CAAgCI,MAAhC,CAAuCC,OAAvC,CAAA,CAAgDC,IAAhD,CAAqD,GAArD,CAA1B,CAAAT;KAEH;IAED,OAAO;QAAE,GAAGtB,SAAL;QAAgB,GAAGsB,aAAH;KAAvB,CAAO;CACR;AAED,MAAM7B,yCAAI,GAAGF,yCAAb,AAAA;;AD3HA", "sources": ["packages/react/slot/src/index.ts", "packages/react/slot/src/Slot.tsx"], "sourcesContent": ["export {\n  Slot,\n  Slottable,\n  //\n  Root,\n} from './Slot';\nexport type { SlotProps } from './Slot';\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children as React.ReactNode;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props.children as React.ReactNode)\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      ref: forwardedRef ? composeRefs(forwardedRef, (children as any).ref) : (children as any).ref,\n    });\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(child: React.ReactNode): child is React.ReactElement {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n"], "names": ["Slot", "Slottable", "Root", "React", "composeRefs", "forwardRef", "props", "forwardedRef", "children", "slotProps", "childrenA<PERSON>y", "Children", "toArray", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "count", "only", "isValidElement", "cloneElement", "undefined", "displayName", "SlotClone", "mergeProps", "ref", "type", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "<PERSON><PERSON><PERSON><PERSON>", "test", "args", "filter", "Boolean", "join"], "version": 3, "file": "index.js.map"}