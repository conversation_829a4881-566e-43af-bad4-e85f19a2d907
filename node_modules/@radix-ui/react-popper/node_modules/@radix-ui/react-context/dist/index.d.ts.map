{"mappings": ";AAEA,8BAAuB,gBAAgB,SAAS,MAAM,GAAG,IAAI,EAC3D,iBAAiB,EAAE,MAAM,EACzB,cAAc,CAAC,EAAE,gBAAgB;YAIR,gBAAgB,GAAG;QAAE,QAAQ,EAAE,MAAM,SAAS,CAAA;KAAE;;kBAQvC,MAAM,uBAUzC;AAMD,kBAAW,CAAC,GAAG,GAAG,IAAI;IAAE,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,CAAA;CAAE,GAAG,SAAS,CAAC;AAC9E,iBAAiB,CAAC,KAAK,EAAE,KAAK,KAAK;IAAE,CAAC,WAAW,EAAE,MAAM,GAAG,KAAK,CAAA;CAAE,CAAC;AACpE;IACE,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,SAAS,CAAC;CACf;AAED,mCAA4B,SAAS,EAAE,MAAM,EAAE,sBAAsB,GAAE,WAAW,EAAO,yEAQlE,MAAM;;;kBAQ+C,MAAM,SAAS;;;kBAUrD,MAAM,iFAgC3C", "sources": ["packages/react/context/src/packages/react/context/src/createContext.tsx", "packages/react/context/src/packages/react/context/src/index.ts", "packages/react/context/src/index.ts"], "sourcesContent": [null, null, "export { createContext, createContextScope } from './createContext';\nexport type { CreateScope, Scope } from './createContext';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}