{"mappings": ";;;;;A;;;;ACIA,MAAMM,2BAAK,GAAG;IACZ,GADY;IAEZ,QAFY;IAGZ,KAHY;IAIZ,MAJY;IAKZ,IALY;IAMZ,IANY;IAOZ,KAPY;IAQZ,OARY;IASZ,OATY;IAUZ,IAVY;IAWZ,KAXY;IAYZ,IAZY;IAaZ,GAbY;IAcZ,MAdY;IAeZ,KAfY;IAgBZ,IAhBY;CAAd,A,EAmBA,8CAnBA;AAoBA,gEAAA;AACA,kBAAA;AAcA;;oGAEA,CAEA,MAAMN,yCAAS,GAAGM,2BAAK,CAACC,MAAN,CAAa,CAACC,SAAD,EAAYC,IAAZ,GAAqB;IAClD,MAAMC,IAAI,GAAA,aAAGP,CAAAA,iBAAA,CAAiB,CAACS,KAAD,EAA4CC,YAA5C,GAAkE;QAC9F,MAAM,E,SAAEC,OAAF,CAAA,EAAW,GAAGC,cAAH,EAAX,GAAiCH,KAAvC,AAAM;QACN,MAAMI,IAAS,GAAGF,OAAO,GAAGT,WAAH,GAAUI,IAAnC,AAAA;QAEAN,gBAAA,CAAgB,IAAM;YACnBe,MAAD,CAAgBC,MAAM,CAACC,GAAP,CAAW,UAAX,CAAhB,CAAA,GAA0C,IAA1C,CAACF;SADH,EAEG,EAFH,CAEC,CAAA;QAED,OAAA,aAAO,CAAA,oBAAA,CAAC,IAAD,EAAA,oCAAA,CAAA,EAAA,EAAUH,cAAV,EAAP;YAAiC,GAAG,EAAEF,YAAL;SAA1B,CAAA,CAAP,CAAO;KARI,CAAb,AASC;IAEDH,IAAI,CAACW,WAAL,GAAoB,CAAA,UAAA,EAAYZ,IAAK,CAAA,CAArC,CAAAC;IAEA,OAAO;QAAE,GAAGF,SAAL;QAAgB,CAACC,IAAD,CAAA,EAAQC,IAAR;KAAvB,CAAO;CAdS,EAef,EAfe,CAAlB,AAeC;AAED;;oGAEA,CAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCA,CAEA,SAASR,yCAAT,CAA4DoB,MAA5D,EAAiFC,KAAjF,EAA2F;IACzF,IAAID,MAAJ,EAAYlB,gBAAA,CAAmB,IAAMkB,MAAM,CAACG,aAAP,CAAqBF,KAArB,CAAzB;IAAA,CAAZ,CAAA;CACD;AAED,oGAAA,CAEA,MAAMtB,yCAAI,GAAGD,yCAAb,AAAA;;AD3GA", "sources": ["packages/react/primitive/src/index.ts", "packages/react/primitive/src/Primitive.tsx"], "sourcesContent": ["export {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n} from './Primitive';\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef } from './Primitive';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { Slot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\n// Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\ntype PropsWithoutRef<P> = P extends any ? ('ref' extends keyof P ? Pick<P, Exclude<keyof P, 'ref'>> : P) : P;\ntype ComponentPropsWithoutRef<T extends React.ElementType> = PropsWithoutRef<\n  React.ComponentProps<T>\n>;\n\ntype Primitives = { [E in typeof NODES[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    React.useEffect(() => {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }, []);\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef };\n"], "names": ["Primitive", "Root", "dispatchDiscreteCustomEvent", "React", "ReactDOM", "Slot", "NODES", "reduce", "primitive", "node", "Node", "forwardRef", "props", "forwardedRef", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "useEffect", "window", "Symbol", "for", "displayName", "target", "event", "flushSync", "dispatchEvent"], "version": 3, "file": "index.mjs.map"}