# 哔哩哔哩自动回复桌面应用 - 技术规格文档

## 问题陈述
- **业务问题**: 用户需要手动处理大量B站评论、私信和新粉丝关注，效率低下且容易遗漏
- **当前状态**: 没有现有的桌面应用可以满足自动回复需求，需要从零开始构建
- **预期结果**: 提供一个跨平台桌面应用，自动监控和回复B站评论、私信和关注，提升用户互动效率

## 解决方案概述
- **方案**: 使用Tauri 2.x框架构建跨平台桌面应用，Rust后端处理B站API调用，React TypeScript前端提供用户界面
- **核心变更**: 创建全新的Tauri桌面应用项目，实现B站API集成、自动回复逻辑、用户界面和系统集成
- **成功标准**: 
  - 成功登录B站账号并保持会话
  - 准确监控评论、私信和关注
  - 按配置规则自动回复
  - 提供完整的日志和统计功能
  - 支持macOS和Windows平台

## 技术实现

### 项目结构设计

```
bilibili-auto-reply-desktop/
├── src-tauri/                    # Rust后端
│   ├── src/
│   │   ├── main.rs              # 主入口
│   │   ├── lib.rs               # 库文件
│   │   ├── commands/            # Tauri命令模块
│   │   │   ├── mod.rs
│   │   │   ├── auth.rs          # 登录相关命令
│   │   │   ├── reply.rs         # 回复相关命令
│   │   │   ├── config.rs        # 配置管理命令
│   │   │   └── stats.rs         # 统计相关命令
│   │   ├── services/            # 业务逻辑服务
│   │   │   ├── mod.rs
│   │   │   ├── bilibili_api.rs  # B站API服务
│   │   │   ├── comment_monitor.rs # 评论监控服务
│   │   │   ├── dm_monitor.rs    # 私信监控服务
│   │   │   ├── follow_monitor.rs # 关注监控服务
│   │   │   └── scheduler.rs     # 任务调度服务
│   │   ├── models/              # 数据模型
│   │   │   ├── mod.rs
│   │   │   ├── config.rs        # 配置数据结构
│   │   │   ├── user.rs          # 用户数据结构
│   │   │   ├── log.rs           # 日志数据结构
│   │   │   └── stats.rs         # 统计数据结构
│   │   ├── utils/               # 工具函数
│   │   │   ├── mod.rs
│   │   │   ├── crypto.rs        # 加密解密
│   │   │   ├── storage.rs       # 本地存储
│   │   │   └── network.rs       # 网络请求
│   │   └── error.rs             # 错误定义
│   ├── Cargo.toml              # Rust依赖配置
│   ├── tauri.conf.json         # Tauri配置
│   ├── build.rs               # 构建脚本
│   └── icons/                 # 应用图标
├── src/                       # React前端
│   ├── main.tsx              # 入口文件
│   ├── App.tsx               # 根组件
│   ├── components/           # UI组件
│   │   ├── ui/              # shadcn/ui基础组件
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── card.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── select.tsx
│   │   │   ├── switch.tsx
│   │   │   ├── table.tsx
│   │   │   ├── tabs.tsx
│   │   │   └── toast.tsx
│   │   ├── layout/          # 布局组件
│   │   │   ├── Sidebar.tsx  # 左侧导航
│   │   │   ├── Header.tsx   # 顶部状态栏
│   │   │   └── MainLayout.tsx # 主布局
│   │   ├── features/        # 功能组件
│   │   │   ├── auth/        # 登录相关
│   │   │   │   ├── LoginDialog.tsx
│   │   │   │   ├── QRCode.tsx
│   │   │   │   └── UserInfo.tsx
│   │   │   ├── reply/       # 回复配置
│   │   │   │   ├── CommentReply.tsx
│   │   │   │   ├── DMReply.tsx
│   │   │   │   ├── FollowReply.tsx
│   │   │   │   └── ReplyTemplate.tsx
│   │   │   ├── logs/        # 日志管理
│   │   │   │   ├── LogViewer.tsx
│   │   │   │   ├── LogFilter.tsx
│   │   │   │   └── LogExport.tsx
│   │   │   ├── stats/       # 统计数据
│   │   │   │   ├── Dashboard.tsx
│   │   │   │   ├── ReplyStats.tsx
│   │   │   │   └── Chart.tsx
│   │   │   └── settings/    # 系统设置
│   │   │       ├── General.tsx
│   │   │       ├── Theme.tsx
│   │   │       └── About.tsx
│   │   └── common/          # 通用组件
│   │       ├── LoadingSpinner.tsx
│   │       ├── ErrorBoundary.tsx
│   │       └── ConfirmDialog.tsx
│   ├── hooks/               # React Hooks
│   │   ├── useConfig.ts     # 配置管理Hook
│   │   ├── useLogs.ts       # 日志管理Hook
│   │   ├── useStats.ts      # 统计数据Hook
│   │   └── useAuth.ts       # 认证状态Hook
│   ├── stores/              # 状态管理
│   │   ├── authStore.ts     # 认证状态
│   │   ├── configStore.ts   # 配置状态
│   │   ├── logStore.ts      # 日志状态
│   │   └── appStore.ts      # 应用状态
│   ├── types/               # TypeScript类型定义
│   │   ├── api.ts           # API接口类型
│   │   ├── config.ts        # 配置类型
│   │   ├── user.ts          # 用户类型
│   │   └── common.ts        # 通用类型
│   ├── utils/               # 工具函数
│   │   ├── api.ts           # API调用封装
│   │   ├── format.ts        # 格式化函数
│   │   └── validation.ts    # 表单验证
│   └── styles/              # 样式文件
│       ├── globals.css      # 全局样式
│       └── components.css   # 组件样式
├── public/                  # 静态资源
├── package.json            # Node.js依赖
├── tailwind.config.js      # Tailwind配置
├── components.json         # shadcn/ui配置
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── README.md               # 项目说明
```

### 数据库变更
**本地存储结构**:

```json
// ~/.bilibili-auto-reply/config.json
{
  "user": {
    "uid": "123456789",
    "username": "用户名",
    "avatar": "头像URL",
    "cookie_expire": "2024-12-31T23:59:59Z"
  },
  "reply_settings": {
    "comment": {
      "enabled": true,
      "scan_interval": 180,
      "reply_delay": 3,
      "monitor_mode": "all", // "all" | "specific"
      "video_list": [],
      "templates": ["模板1", "模板2"],
      "exclude_keywords": ["关键词1"]
    },
    "follow": {
      "enabled": true,
      "check_interval": 300,
      "reply_delay_min": 5,
      "reply_delay_max": 30,
      "welcome_template": "欢迎关注!"
    },
    "dm": {
      "enabled": true,
      "check_interval": 180,
      "reply_delay": 3,
      "only_fans": false,
      "keyword_replies": {
        "关键词": "回复内容"
      },
      "default_reply": "默认回复"
    }
  },
  "app_settings": {
    "theme": "system", // "light" | "dark" | "system"
    "auto_start": false,
    "minimize_to_tray": true,
    "silent_mode": false
  }
}

// ~/.bilibili-auto-reply/logs.json
{
  "logs": [
    {
      "id": "uuid",
      "timestamp": "2024-08-19T10:30:00Z",
      "type": "comment_reply", // "comment_reply" | "follow_reply" | "dm_reply" | "system"
      "status": "success", // "success" | "error"
      "message": "成功回复评论",
      "details": {
        "video_bvid": "BV1xxx",
        "comment_id": "123",
        "reply_content": "回复内容"
      }
    }
  ]
}

// ~/.bilibili-auto-reply/stats.json
{
  "daily_stats": {
    "2024-08-19": {
      "comment_replies": 15,
      "follow_replies": 3,
      "dm_replies": 8,
      "success_rate": 0.95
    }
  },
  "total_stats": {
    "total_comment_replies": 1250,
    "total_follow_replies": 89,
    "total_dm_replies": 456,
    "app_runtime_hours": 120.5
  }
}

// ~/.bilibili-auto-reply/encrypted_cookies.dat (加密存储)
```

### 代码变更

#### 新建文件列表
1. **Rust后端文件**:
   - `src-tauri/src/main.rs` - Tauri主入口
   - `src-tauri/src/lib.rs` - 库定义
   - `src-tauri/src/commands/*.rs` - Tauri命令定义
   - `src-tauri/src/services/*.rs` - 业务逻辑服务
   - `src-tauri/src/models/*.rs` - 数据结构定义
   - `src-tauri/src/utils/*.rs` - 工具函数

2. **前端文件**:
   - `src/main.tsx` - React入口
   - `src/App.tsx` - 根组件
   - `src/components/**/*.tsx` - UI组件
   - `src/hooks/*.ts` - React Hooks
   - `src/stores/*.ts` - 状态管理
   - `src/types/*.ts` - 类型定义

3. **配置文件**:
   - `src-tauri/Cargo.toml` - Rust依赖
   - `src-tauri/tauri.conf.json` - Tauri配置
   - `package.json` - Node.js依赖
   - `tailwind.config.js` - Tailwind配置

#### 核心函数签名

**Tauri Commands**:
```rust
// src-tauri/src/commands/auth.rs
#[tauri::command]
async fn get_login_qr() -> Result<String, String>

#[tauri::command]
async fn check_login_status(qr_key: String) -> Result<LoginResult, String>

#[tauri::command]
async fn logout() -> Result<(), String>

// src-tauri/src/commands/config.rs
#[tauri::command]
async fn load_config() -> Result<AppConfig, String>

#[tauri::command]
async fn save_config(config: AppConfig) -> Result<(), String>

// src-tauri/src/commands/reply.rs
#[tauri::command]
async fn start_monitoring() -> Result<(), String>

#[tauri::command]
async fn stop_monitoring() -> Result<(), String>

#[tauri::command]
async fn get_monitoring_status() -> Result<MonitoringStatus, String>

// src-tauri/src/commands/stats.rs
#[tauri::command]
async fn get_daily_stats(date: String) -> Result<DailyStats, String>

#[tauri::command]
async fn get_logs(filter: LogFilter) -> Result<Vec<LogEntry>, String>
```

### API变更

#### Tauri Commands接口设计

**认证相关**:
```typescript
// 获取登录二维码
invoke('get_login_qr'): Promise<string>

// 检查登录状态  
invoke('check_login_status', { qrKey: string }): Promise<LoginResult>

// 退出登录
invoke('logout'): Promise<void>
```

**配置管理**:
```typescript
// 加载配置
invoke('load_config'): Promise<AppConfig>

// 保存配置
invoke('save_config', { config: AppConfig }): Promise<void>

// 重置配置
invoke('reset_config'): Promise<void>
```

**回复服务**:
```typescript
// 启动监控
invoke('start_monitoring'): Promise<void>

// 停止监控
invoke('stop_monitoring'): Promise<void>

// 获取监控状态
invoke('get_monitoring_status'): Promise<MonitoringStatus>
```

**日志统计**:
```typescript
// 获取日志
invoke('get_logs', { filter: LogFilter }): Promise<LogEntry[]>

// 导出日志
invoke('export_logs', { format: 'json' | 'csv' }): Promise<string>

// 获取统计数据
invoke('get_daily_stats', { date: string }): Promise<DailyStats>

// 清空日志
invoke('clear_logs'): Promise<void>
```

#### 事件系统
```typescript
// 监听日志更新
listen('log-update', (event: { payload: LogEntry }) => void)

// 监听状态变更
listen('status-change', (event: { payload: MonitoringStatus }) => void)

// 监听错误事件
listen('error', (event: { payload: { message: string, details?: any } }) => void)
```

### 配置变更

#### Tauri配置 (tauri.conf.json)
```json
{
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devPath": "http://localhost:1420",
    "distDir": "../dist",
    "withGlobalTauri": false
  },
  "package": {
    "productName": "哔哩哔哩自动回复",
    "version": "1.0.0"
  },
  "tauri": {
    "allowlist": {
      "all": false,
      "fs": {
        "readFile": true,
        "writeFile": true,
        "createDir": true,
        "scope": ["$APPDATA/*", "$HOME/.bilibili-auto-reply/*"]
      },
      "path": {
        "all": true
      },
      "notification": {
        "all": true
      },
      "app": {
        "show": true,
        "hide": true
      },
      "systemTray": {
        "all": true
      }
    },
    "bundle": {
      "active": true,
      "category": "Productivity",
      "copyright": "",
      "deb": {
        "depends": []
      },
      "externalBin": [],
      "icon": [
        "icons/32x32.png",
        "icons/128x128.png",
        "icons/<EMAIL>",
        "icons/icon.icns",
        "icons/icon.ico"
      ],
      "identifier": "com.bilibili.auto-reply",
      "longDescription": "",
      "macOS": {
        "entitlements": null,
        "exceptionDomain": "",
        "frameworks": [],
        "providerShortName": null,
        "signingIdentity": null
      },
      "resources": [],
      "shortDescription": "",
      "targets": "all",
      "windows": {
        "certificateThumbprint": null,
        "digestAlgorithm": "sha256",
        "timestampUrl": ""
      }
    },
    "security": {
      "csp": null
    },
    "systemTray": {
      "iconPath": "icons/icon.png",
      "iconAsTemplate": true,
      "menuOnLeftClick": false
    },
    "updater": {
      "active": false
    },
    "windows": [
      {
        "fullscreen": false,
        "height": 800,
        "resizable": true,
        "title": "哔哩哔哩自动回复",
        "width": 1200,
        "minWidth": 1000,
        "minHeight": 700
      }
    ]
  }
}
```

#### 依赖配置

**Cargo.toml**:
```toml
[dependencies]
tauri = { version = "2.0", features = ["api-all", "system-tray"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "cookies"] }
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
aes = "0.8"
base64 = "0.21"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
dirs = "5.0"
```

**package.json**:
```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "tauri": "tauri"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@tauri-apps/api": "^2.0.0",
    "zustand": "^4.4.0",
    "lucide-react": "^0.263.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^1.14.0",
    "recharts": "^2.8.0",
    "react-hook-form": "^7.45.0",
    "@hookform/resolvers": "^3.2.0",
    "zod": "^3.22.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@vitejs/plugin-react": "^4.0.3",
    "typescript": "^5.0.2",
    "vite": "^4.4.4",
    "@tauri-apps/cli": "^2.0.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.14",
    "postcss": "^8.4.27"
  }
}
```

## 实现序列

### 阶段1: 项目初始化和基础架构
1. **初始化Tauri项目**
   - 执行 `npm create tauri-app@latest`
   - 配置React + TypeScript模板
   - 安装shadcn/ui和相关依赖

2. **设置基础目录结构**
   - 创建Rust后端模块结构
   - 创建React前端组件结构
   - 配置Tailwind CSS和shadcn/ui

3. **配置开发环境**
   - 设置Tauri配置文件
   - 配置构建脚本
   - 设置系统托盘基础功能

### 阶段2: 核心服务实现
1. **B站API集成服务**
   - 实现登录二维码获取
   - 实现Cookie管理和加密存储
   - 实现评论、私信、关注API调用

2. **监控服务实现**
   - 实现评论监控逻辑
   - 实现私信监控逻辑
   - 实现关注监控逻辑
   - 实现任务调度器

3. **数据管理服务**
   - 实现配置文件管理
   - 实现日志记录系统
   - 实现统计数据计算

### 阶段3: 用户界面开发
1. **布局组件开发**
   - 实现主布局框架
   - 实现侧边导航栏
   - 实现顶部状态栏

2. **功能页面开发**
   - 实现登录界面和二维码扫描
   - 实现评论回复配置界面
   - 实现私信回复配置界面
   - 实现关注回复配置界面

3. **数据展示开发**
   - 实现实时日志查看器
   - 实现统计数据仪表盘
   - 实现设置管理界面

### 阶段4: 系统集成和优化
1. **系统功能集成**
   - 实现开机自启动
   - 实现最小化到系统托盘
   - 实现系统通知

2. **性能优化**
   - 优化API请求频率
   - 实现内存管理
   - 优化界面渲染性能

3. **错误处理和日志**
   - 完善错误处理机制
   - 实现详细日志记录
   - 添加用户友好的错误提示

### 阶段5: 测试和打包部署
1. **功能测试**
   - 单元测试核心功能
   - 集成测试完整流程
   - 手动测试用户界面

2. **跨平台兼容性**
   - macOS平台测试和优化
   - Windows平台测试和优化
   - 解决平台特定问题

3. **构建和分发**
   - 配置生产环境构建
   - 生成安装包 (dmg/exe)
   - 准备发布文档

## 验证计划

### 单元测试
- **Rust后端测试**:
  - B站API调用功能测试
  - 数据加密/解密功能测试
  - 配置管理功能测试
  - 监控服务逻辑测试

- **前端组件测试**:
  - UI组件渲染测试
  - 用户交互功能测试
  - 状态管理测试
  - API调用封装测试

### 集成测试
- **完整登录流程测试**: 二维码生成 → 扫码登录 → Cookie保存 → 会话验证
- **评论回复流程测试**: 配置设置 → 启动监控 → 检测新评论 → 自动回复 → 日志记录
- **私信回复流程测试**: 关键词配置 → 监控私信 → 匹配关键词 → 自动回复 → 防重复验证
- **关注回复流程测试**: 监控新粉丝 → 检测关注事件 → 发送欢迎私信 → 记录统计

### 业务逻辑验证
- **回复去重验证**: 确保同一评论不会重复回复
- **频率限制验证**: 确保请求频率符合B站API限制
- **错误恢复验证**: 网络异常或API错误时的恢复机制
- **数据持久化验证**: 应用重启后配置和数据完整性
- **跨平台验证**: macOS和Windows平台功能一致性