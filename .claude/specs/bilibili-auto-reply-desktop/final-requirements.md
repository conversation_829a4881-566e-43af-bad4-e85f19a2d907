# 哔哩哔哩自动回复桌面应用 - 最终需求文档

## 项目概述
- **项目名称**: bilibili-auto-reply-desktop
- **技术栈**: Tauri + shadcn/ui + TypeScript
- **平台支持**: macOS + Windows
- **核心功能**: B站评论、关注、私信自动回复

## 核心功能详细规格

### 1. 评论自动回复
- **监控模式**: 全部视频 / 指定视频列表
- **去重逻辑**: 同视频下相同评论只回复一次，跨视频不去重
- **时间设置**: 
  - 扫描频率: 默认3分钟（可自定义1-60分钟）
  - 回复延迟: 默认3秒（可自定义1-30秒）
- **回复模板**: 支持多个模板随机选择
- **过滤功能**: 支持排除关键词设置
- **无回复上限**: 不设置每日回复数量限制

### 2. 关注自动回复
- **回复方式**: 向新粉丝发送私信
- **触发机制**: 检测到新粉丝关注时触发
- **时间设置**: 
  - 检测频率: 默认5分钟（可自定义）
  - 发送延迟: 5-30秒随机延迟
- **回复内容**: 固定欢迎模板
- **防重复**: 不需要记录已回复粉丝

### 3. 私信自动回复
- **匹配逻辑**: 关键词长度优先匹配
- **回复范围**: 默认所有私信，可设置仅回复粉丝
- **时间设置**:
  - 检测频率: 默认3分钟（可自定义）
  - 回复延迟: 默认3秒（可自定义）
- **防骚扰**: 同用户1小时内只回复1次
- **关键词管理**: 支持关键词-回复对应关系配置
- **默认回复**: 无匹配时使用默认模板

### 4. 仪表盘界面
- **布局设计**: 三栏布局（左侧导航 + 中间配置 + 右侧日志）
- **导航菜单**: 评论回复、关注回复、私信回复、统计数据、系统设置、账号管理
- **配置管理**: 手动保存按钮，修改后重启监控服务
- **状态显示**: 顶部账号信息，底部运行状态

### 5. 统计数据页面
- **今日数据**: 各功能回复次数统计
- **图表展示**: 本周每日回复量柱状图
- **成功率统计**: 各功能回复成功率
- **运行统计**: 应用运行时长记录

### 6. 实时日志功能
- **显示方式**: 颜色区分不同操作类型，最新日志在底部
- **颜色编码**: 
  - 🟢 评论回复成功
  - 🔵 关注回复成功
  - 🟡 私信回复成功
  - 🔴 操作失败
  - ⚪ 系统消息
- **日志管理**: 
  - 自动保留30天
  - 支持手动清空
  - JSON格式导出
- **筛选功能**: 操作类型筛选 + 日期范围筛选

### 7. 账号管理功能
- **登录方式**: 扫码登录B站账号
- **登录流程**: 用户主动点击后显示扫码界面
- **信息展示**: 用户名 + 头像 + Cookie有效期（30天）
- **Cookie管理**: 
  - 过期自动提示重新登录
  - 退出登录清除本地Cookie
  - 加密存储Cookie数据
- **账号支持**: 单账号模式

### 8. 系统设置功能
- **主题设置**: 浅色/深色/跟随系统
- **全局开关**: 开机自启动、最小化托盘、静默模式
- **配置管理**: 导出配置、导入配置、重置设置
- **关于信息**: 版本号、检查更新、使用帮助

## 技术实现要求

### 技术栈
- **框架**: Tauri 2.x (Rust后端 + Web前端)
- **前端**: React + TypeScript + shadcn/ui
- **状态管理**: Zustand 或 React Context
- **样式**: Tailwind CSS + shadcn/ui组件
- **数据存储**: 本地JSON文件存储
- **系统集成**: 系统托盘、开机自启动

### 架构设计
- **前后端分离**: Tauri Rust后端处理B站API调用
- **模块化设计**: 每个功能独立模块
- **配置驱动**: 所有行为通过配置文件控制
- **状态同步**: 前后端实时状态同步

### 安全要求
- **Cookie加密**: 使用AES加密存储用户Cookie
- **API限制**: 实现请求频率限制避免被封
- **错误处理**: 完善的错误处理和用户提示
- **数据保护**: 本地数据加密存储

## 质量标准
- **功能完整性**: 所有核心功能正常工作
- **用户体验**: 界面友好，操作直观
- **稳定性**: 长时间运行无崩溃
- **性能**: 响应迅速，资源占用合理
- **兼容性**: Mac和Windows平台正常运行

## 交付物
1. 完整的Tauri桌面应用
2. 源代码和构建脚本
3. 用户使用文档
4. 功能测试用例
5. 安装包(dmg/exe)

---

**需求质量评分: 95/100** ✅
**准备开始实现阶段** 🚀